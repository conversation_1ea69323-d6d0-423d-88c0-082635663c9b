@php
    $lessons = App\Models\Lesson::where('id', $id)->first();
    $sections = App\Models\Section::where('course_id', $lessons->course_id)
        ->orderBy('sort')
        ->get();
    $select_section = App\Models\Section::where('id', $lessons->section_id)->value('title');
@endphp

<div class="alert alert-info d-flex align-items-center py-2" role="alert">
    {{ get_phrase('Lesson type') }}:
    @if ($lessons->lesson_type == 'html5')
        <strong class="text-capitalize ms-1">{{ get_phrase('Video url') . ' [.mp4]' }}</strong>
    @elseif ($lessons->lesson_type == 'system-video')
        <strong class="text-capitalize ms-1">{{ get_phrase('Video file') }}</strong>
    @elseif ($lessons->video_type == 'youtube' || $lessons->video_type == 'vimeo')
        <strong class="text-capitalize ms-1">{{ get_phrase($lessons->video_type) }} {{ get_phrase('Video') }} </strong>
    @elseif($lessons->lesson_type == 'google_drive_video')
        <strong class="text-capitalize ms-1">{{ get_phrase('Google drive video') }}</strong>
    @elseif($lessons->lesson_type == 'document_type')
        <strong class="text-capitalize ms-1">{{ get_phrase('Document file') }}</strong>
    @else
        <strong class="text-capitalize ms-1">{{ $lessons->lesson_type }}</strong>
    @endif
</div>

<!-- ACTUAL LESSON ADDING FORM -->
<form class="ajaxFormSubmission" action="{{ route('admin.lesson.edit') }}" method="post" enctype="multipart/form-data">
    @csrf

    <input type="hidden" name="id" value="{{ $id }}">
    <input type="hidden" name="lesson_type" value="{{ $lessons->lesson_type }}">
    <div class="form-group eForm-group mb-2">
        <label class="form-label ol-form-label">{{ get_phrase('Title') }}</label>
        <input type="text" name="title" class="form-control ol-form-control" value="{{ $lessons->title }}" required>
    </div>

    <div class="form-group mb-2">
        <label class="form-label ol-form-label">{{ get_phrase('Section') }}</label>
        <select class="form-control ol-select2" data-toggle="select2" name="section_id" required>
            @foreach ($sections as $section)
                <option value="{{ $section->id }}" @if ($lessons->section_id == $section->id) selected @endif>
                    {{ $section->title }}</option>
            @endforeach
        </select>
    </div>

    @if ($lessons->lesson_type == 'video-url')
        @include('admin.course.youtube_type_lesson_edit')
    @elseif ($lessons->lesson_type == 'vimeo-url')
        @include('admin.course.vimeo_type_lesson_edit')
    @elseif ($lessons->lesson_type == 'system-video')
        @include('admin.course.video_type_lesson_edit')
    @elseif ($lessons->lesson_type == 'html5')
        @include('admin.course.html5_type_lesson_edit')
    @elseif ($lessons->lesson_type == 'google_drive')
        @include('admin.course.google_drive_type_lesson_edit')
    @elseif ($lessons->lesson_type == 'document_type')
        @include('admin.course.document_type_lesson_edit')
    @elseif ($lessons->lesson_type == 'text')
        @include('admin.course.text_type_lesson_edit')
    @elseif ($lessons->lesson_type == 'image')
        @include('admin.course.image_file_type_lesson_edit')
    @elseif ($lessons->lesson_type == 'iframe')
        @include('admin.course.iframe_type_lesson_edit')
    @endif

    <div class="row">
        <div class="col-md-3">
            <div class="form-group mb-2">
                <label class="form-label ol-form-label">Trả phí</label>
                <div class="form-check form-switch">
                    <input class="form-check-input form-switch-medium" name="paid_lesson" type="checkbox"
                           @if( $lessons->paid_lesson) checked @endif>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group mb-2">
                <label class="form-label ol-form-label">Học thử pro</label>
                <div class="form-check form-switch">
                    <input class="form-check-input form-switch-medium" name="trial_lesson" type="checkbox"
                           @if( $lessons->trial_lesson) checked @endif>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group mb-2">
                <label class="form-label ol-form-label">Quan trọng</label>
                <div class="form-check form-switch">
                    <input class="form-check-input form-switch-medium" name="is_important" type="checkbox"
                           @if( $lessons->is_important) checked @endif>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group mb-2">
                <label class="form-label ol-form-label">Ẩn tiêu đề</label>
                <div class="form-check form-switch">
                    <input class="form-check-input form-switch-medium" name="hide_title" type="checkbox"
                           @if( $lessons->hide_title) checked @endif>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group mb-2">
        <label class="form-label ol-form-label">{{ get_phrase('summary') }}</label>
        <textarea name="summary" class="form-control text_editor">{{ $lessons->summary }}</textarea>
    </div>

    <div class="form-group mb-3">
        <label class="form-label ol-form-label">{{ get_phrase('Thumbnail Image') }}</label>
        <div class="input-group">
            <input type="file" class="form-control ol-form-control" name="thumbnail" accept="image/*">
            <span class="input-group-text"><i class="fi fi-rr-picture"></i></span>
        </div>
        <small class="text-muted">{{ get_phrase('Recommended dimension') }}: 800x450 (16:9)</small>

        @if($lessons->thumbnail)
            <div class="mt-2">
                <label class="form-label ol-form-label">{{ get_phrase('Current Thumbnail') }}</label>
                <div class="thumbnail-preview">
                    <img src="{{ asset('uploads/thumbnails/'.$lessons->thumbnail) }}"
                         alt="{{ $lessons->title }}" class="img-fluid rounded" style="max-height: 150px;">
                </div>
            </div>
        @endif
    </div>
    @if(addon_check('my.video_advertisement'))
    <div class="form-group mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3" id="popupSettings" style="cursor: pointer;" onclick="togglePopupSettings()">
                <div class="d-flex align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="icon-wrapper me-3" style="width: 40px; height: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                            <i class="fi fi-rr-megaphone text-white fs-16px"></i>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-semibold text-dark">Cài đặt quảng cáo popup</h5>
                            <small class="text-muted">Quản lý quảng cáo hiển thị trong video bài học</small>
                        </div>
                    </div>
                    <i class="fi fi-rr-angle-small-down ms-auto text-muted fs-18px" id="popupIcon" style="transition: transform 0.3s ease;"></i>
                </div>
            </div>
            <div class="card-body p-4" id="popupSettingsBody" style="display: none; background: #fafbfc;">
                <div class="mb-4">
                    <div class="d-flex align-items-center justify-content-between p-3 rounded-3" style="background: white; border: 1px solid #e9ecef;">
                        <div class="d-flex align-items-center">
                            <div class="form-check form-switch me-3">
                                <input class="form-check-input" id="enablePopups" type="checkbox" style="width: 3rem; height: 1.5rem;"
                                       @if(count($lessons->popup_configs ?? []) > 0) checked @endif>
                            </div>
                            <div>
                                <label class="form-check-label fw-medium text-dark mb-0" for="enablePopups">
                                    Bật quảng cáo popup cho bài học này
                                </label>
                                <div class="text-muted small">Hiển thị quảng cáo tại các thời điểm cụ thể trong video</div>
                            </div>
                        </div>
                        <div class="badge bg-light text-muted" id="popupCount">
                            <span class="popup-count">{{ count($lessons->popup_configs ?? []) }}</span> popup
                        </div>
                    </div>
                </div>

                <div id="popupConfigContainer" @if(count($lessons->popup_configs ?? []) == 0) style="display: none;" @endif>
                    @php
                        $popupConfigs = $lessons->popup_configs ?? [];
                        $popups = \App\Models\Popup::where('status', 'active')->get();
                    @endphp

                    @if(count($popupConfigs) > 0)
                        @foreach($popupConfigs as $index => $config)
                            <div class="popup-config-item mb-3 card border-0 shadow-sm" style="background: white;">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="drag-handle me-3" style="cursor: move; color: #6c757d;">
                                                <i class="fi fi-rr-menu-dots-vertical handle"></i>
                                            </div>
                                            <div class="popup-header">
                                                <span class="badge bg-primary">Popup #<span class="index-number">{{ $index + 1 }}</span></span>
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-popup-config" style="border-radius: 8px;">
                                            <i class="fi fi-rr-trash me-1"></i> Xóa
                                        </button>
                                    </div>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label fw-medium text-dark mb-2">
                                                    <i class="fi fi-rr-rectangle-list me-2 text-primary"></i>Chọn popup
                                                </label>
                                                <div class="position-relative">
                                                    <select class="form-select popup-select" name="popup_config[{{ $index }}][popup_id]"
                                                            style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.875rem 1rem; font-size: 0.95rem; background-color: #fff; transition: all 0.3s ease;">
                                                        <option value="" disabled selected>🎯 Chọn popup để hiển thị</option>
                                                        @foreach($popups as $popup)
                                                            <option value="{{ $popup->id }}" @if($config['popup_id'] == $popup->id) selected @endif>
                                                                📢 {{ $popup->name ?: ($popup->url ? $popup->url : 'Popup #'.$popup->id) }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="alert alert-danger mt-2 popup-duplicate-warning" style="display: none; padding: 0.75rem; font-size: 0.875rem; border-radius: 8px; border: 1px solid #f5c6cb; background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);">
                                                    <i class="fi fi-rr-exclamation-triangle me-2"></i> Popup này đã được lên lịch tại thời điểm này!
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label fw-medium text-dark mb-2">Hiển thị tại (HH:MM:SS)</label>
                                                <input type="text" class="form-control popup-time"
                                                    name="popup_config[{{ $index }}][time]" value="{{ isset($config['time']) ? gmdate('H:i:s', $config['time']) : '00:00:30' }}"
                                                    placeholder="00:00:30" pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}"
                                                    style="border-radius: 8px; border: 1px solid #dee2e6; padding: 0.75rem;">
                                                <small class="text-muted mt-1 d-block">Định dạng: Giờ:Phút:Giây</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="popup-config-item mb-3 card border-0 shadow-sm" style="background: white;">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="drag-handle me-3" style="cursor: move; color: #6c757d;">
                                            <i class="fi fi-rr-menu-dots-vertical handle"></i>
                                        </div>
                                        <div class="popup-header">
                                            <span class="badge bg-primary">Popup #<span class="index-number">1</span></span>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger remove-popup-config" style="border-radius: 8px;">
                                        <i class="fi fi-rr-trash me-1"></i> Xóa
                                    </button>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label fw-medium text-dark mb-2">
                                                <i class="fi fi-rr-rectangle-list me-2 text-primary"></i>Chọn popup
                                            </label>
                                            <div class="position-relative">
                                                <select class="form-select popup-select" name="popup_config[0][popup_id]"
                                                        style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.875rem 1rem; font-size: 0.95rem; background-color: #fff; transition: all 0.3s ease;">
                                                    <option value="" disabled selected>🎯 Chọn popup để hiển thị</option>
                                                    @foreach($popups as $popup)
                                                        <option value="{{ $popup->id }}" @if($lessons->popup_id == $popup->id) selected @endif>
                                                            📢 {{ $popup->name ?: ($popup->url ? $popup->url : 'Popup #'.$popup->id) }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="alert alert-danger mt-2 popup-duplicate-warning" style="display: none; padding: 0.75rem; font-size: 0.875rem; border-radius: 8px; border: 1px solid #f5c6cb; background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);">
                                                <i class="fi fi-rr-exclamation-triangle me-2"></i> Popup này đã được lên lịch tại thời điểm này!
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label fw-medium text-dark mb-2">Hiển thị tại (HH:MM:SS)</label>
                                            <input type="text" class="form-control popup-time"
                                                name="popup_config[0][time]" value="{{ isset($lessons->popup_time) ? gmdate('H:i:s', $lessons->popup_time) : '00:00:30' }}"
                                                placeholder="00:00:30" pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}"
                                                style="border-radius: 8px; border: 1px solid #dee2e6; padding: 0.75rem;">
                                            <small class="text-muted mt-1 d-block">Định dạng: Giờ:Phút:Giây</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="text-center mt-4" id="addPopupBtnContainer" @if(count($lessons->popup_configs ?? []) == 0) style="display: none;" @endif>
                    <button type="button" id="addPopupConfigBtn" class="btn btn-outline-primary px-4 py-2" style="border-radius: 10px; border-width: 2px; font-weight: 500;">
                        <i class="fi fi-rr-plus me-2"></i> Thêm popup khác
                    </button>
                </div>

                <!-- Template for new popup config item (hidden) -->
                <template id="popupConfigTemplate">
                    <div class="popup-config-item mb-3 card border-0 shadow-sm" style="background: white;">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="drag-handle me-3" style="cursor: move; color: #6c757d;">
                                        <i class="fi fi-rr-menu-dots-vertical handle"></i>
                                    </div>
                                    <div class="popup-header">
                                        <span class="badge bg-primary">Popup #<span class="index-number"></span></span>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger remove-popup-config" style="border-radius: 8px;">
                                    <i class="fi fi-rr-trash me-1"></i> Xóa
                                </button>
                            </div>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label fw-medium text-dark mb-2">
                                            <i class="fi fi-rr-rectangle-list me-2 text-primary"></i>Chọn popup
                                        </label>
                                        <div class="position-relative">
                                            <select class="form-select popup-select" name="popup_config[INDEX][popup_id]"
                                                    style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.875rem 1rem; font-size: 0.95rem; background-color: #fff; transition: all 0.3s ease;">
                                                <option value="" disabled selected>🎯 Chọn popup để hiển thị</option>
                                                @foreach($popups as $popup)
                                                    <option value="{{ $popup->id }}">
                                                        📢 {{ $popup->name ?: ($popup->url ? $popup->url : 'Popup #'.$popup->id) }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="alert alert-danger mt-2 popup-duplicate-warning" style="display: none; padding: 0.75rem; font-size: 0.875rem; border-radius: 8px; border: 1px solid #f5c6cb; background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);">
                                            <i class="fi fi-rr-exclamation-triangle me-2"></i> Popup này đã được lên lịch tại thời điểm này!
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label fw-medium text-dark mb-2">Hiển thị tại (HH:MM:SS)</label>
                                        <input type="text" class="form-control popup-time"
                                            name="popup_config[INDEX][time]" value="00:00:30"
                                            placeholder="00:00:30" pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}"
                                            style="border-radius: 8px; border: 1px solid #dee2e6; padding: 0.75rem;">
                                        <small class="text-muted mt-1 d-block">Định dạng: Giờ:Phút:Giây</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
    @endif
    <div class="form-group mb-2 d-none">
        <label class="form-label ol-form-label">{{ get_phrase('Do you want to keep it free as a preview lesson') }}
            ?</label>
        <br>
        <input type="checkbox" name="free_lesson" id="free_lesson" value="1">
        <label for="free_lesson">{{ get_phrase('Mark as free lesson') }}</label>
    </div>

    <div class="text-center">
        <button class="btn ol-btn-primary ol-btn-sm w-100 formSubmissionBtn" type="submit"
                name="button">{{ get_phrase('Update lesson') }}</button>
    </div>
</form>

<script>
    'use strict';
    $(document).ready(function () {
        // Khởi tạo Select2 cho tất cả các dropdown popup
        initializeSelect2Popups();

        // Kiểm tra trùng lặp ban đầu
        checkDuplicatePopups();

        // Kích hoạt sự kiện change cho enablePopups để xử lý đúng
        $('#enablePopups').trigger('change');

        // Form submission handling
        $('form').on('submit', function(e) {
            // Validate all time inputs
            let hasInvalidTime = false;
            $('.popup-time').each(function() {
                const timeValue = $(this).val();
                if (!validateTimeFormat(timeValue)) {
                    $(this).addClass('is-invalid');
                    if (!$(this).next('.invalid-feedback').length) {
                        $(this).after('<div class="invalid-feedback">Invalid time format. Use HH:MM:SS</div>');
                    }
                    hasInvalidTime = true;
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.invalid-feedback').remove();

                    // Convert time to seconds before submission
                    const seconds = timeToSeconds(timeValue);
                    // Create a hidden input with the converted value
                    const hiddenName = $(this).attr('name') + '_seconds';
                    $('input[name="' + hiddenName + '"]').remove(); // Remove if already exists
                    $(this).after('<input type="hidden" name="' + hiddenName + '" value="' + seconds + '">');
                }
            });

            if (hasInvalidTime) {
                e.preventDefault();
                return false;
            }
        });

        // Add validation on time input change
        $(document).on('change blur', '.popup-time', function() {
            const timeValue = $(this).val();
            if (!validateTimeFormat(timeValue)) {
                $(this).addClass('is-invalid');
                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">Invalid time format. Use HH:MM:SS</div>');
                }
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        });

        $('input[name="paid_lesson"]').click(function () {
            if ($(this).is(':checked')) {
                $('input[name="trial_lesson"]').prop('checked', false);
            }
        });

        $('input[name="trial_lesson"]').click(function () {
            if ($(this).is(':checked')) {
                $('input[name="paid_lesson"]').prop('checked', false);
            }
        });

        // Popup config management
        $('#addPopupConfigBtn').on('click', function() {
            addNewPopupConfig();
        });

        // Handle remove popup config
        $(document).on('click', '.remove-popup-config', function() {
            const $item = $(this).closest('.popup-config-item');

            if ($('.popup-config-item').length > 1) {
                $item.fadeOut(300, function() {
                    $(this).remove();
                    updatePopupIndices();
                    updatePopupCount();
                    checkDuplicatePopups();
                });
            } else {
                // Nếu đây là popup duy nhất còn lại, thì ẩn hoặc xóa trắng thay vì xóa phần tử
                $item.find('.popup-select').val('').trigger('change');
                $item.find('.popup-time').val('00:00:30');

                // Tắt công tắc vì không còn popup nào được cấu hình
                $('#enablePopups').prop('checked', false).trigger('change');
            }
        });

        // Xử lý khi thay đổi trạng thái công tắc popup
        $('#enablePopups').on('change', function() {
            if ($(this).is(':checked')) {
                $('#popupConfigContainer').slideDown(300);
                $('#addPopupBtnContainer').slideDown(300);
                // Nếu không có popup nào, thêm một cấu hình mặc định
                if ($('.popup-config-item').length === 0) {
                    addNewPopupConfig();
                }
                $('input[name="popup_config_disabled"]').remove(); // Xóa trường ẩn nếu có
            } else {
                $('#popupConfigContainer').slideUp(300);
                $('#addPopupBtnContainer').slideUp(300);
                // Thêm trường ẩn để chỉ ra rằng popup_config đã bị tắt
                $('form').append('<input type="hidden" name="popup_config_disabled" value="1">');
            }
            updatePopupCount();
        });

        // Xử lý khi thay đổi lựa chọn popup - kiểm tra trùng lặp
        $(document).on('change', '.popup-select', function() {
            checkDuplicatePopups();
        });

        // Initialize sortable for drag-and-drop
        if (typeof Sortable !== 'undefined') {
            new Sortable(document.getElementById('popupConfigContainer'), {
                handle: '.handle',
                animation: 150,
                onEnd: function() {
                    updatePopupIndices();
                }
            });
        }
    });

    // Khởi tạo Select2 cho các dropdown popup
    function initializeSelect2Popups() {
        if ($.fn.select2) {
            $('.popup-select').each(function() {
                if (!$(this).data('select2')) {
                    $(this).select2({
                        dropdownParent: $(this).closest('.popup-config-item')
                    });
                }
            });
        }
    }

    // Kiểm tra popup trùng lặp - Cho phép chọn cùng popup với các thời điểm khác nhau
    function checkDuplicatePopups() {
        let popupTimeMap = {};
        let hasDuplicate = false;

        // Thu thập tất cả cặp popup-id và thời gian
        $('.popup-config-item').each(function() {
            const popupId = $(this).find('.popup-select').val();
            const timeValue = $(this).find('.popup-time').val();

            if (popupId && popupId !== '') {
                // Tạo key map dựa trên popup ID và thời gian
                const mapKey = popupId + '_' + timeValue;

                if (popupTimeMap[mapKey]) {
                    popupTimeMap[mapKey]++;
                } else {
                    popupTimeMap[mapKey] = 1;
                }
            }
        });

        // Kiểm tra trùng lặp chính xác (cùng popup và cùng thời điểm)
        $('.popup-config-item').each(function() {
            const popupId = $(this).find('.popup-select').val();
            const timeValue = $(this).find('.popup-time').val();
            const warningElement = $(this).find('.popup-duplicate-warning');

            if (popupId && popupId !== '') {
                const mapKey = popupId + '_' + timeValue;

                if (popupTimeMap[mapKey] > 1) {
                    // Chỉ cảnh báo khi trùng cả popup ID và thời gian
                    warningElement.show();
                    hasDuplicate = true;
                } else {
                    warningElement.hide();
                }
            } else {
                warningElement.hide();
            }
        });

        // Vô hiệu hóa nút Submit nếu có trùng lặp
        if (hasDuplicate) {
            $('.formSubmissionBtn').prop('disabled', true).addClass('disabled');
        } else {
            $('.formSubmissionBtn').prop('disabled', false).removeClass('disabled');
        }

        return !hasDuplicate;
    }

    // Function to add new popup config
    function addNewPopupConfig() {
        var template = document.getElementById('popupConfigTemplate').innerHTML;
        var index = $('.popup-config-item').length;
        template = template.replace(/INDEX/g, index);

        $('#popupConfigContainer').append(template);

        // Update index numbers
        updatePopupIndices();

        // Initialize select2 for new popup select
        initializeSelect2Popups();

        // Update popup count
        updatePopupCount();

        // Add animation to new item
        $('.popup-config-item:last').hide().fadeIn(300);
    }

    // Function to update popup indices
    function updatePopupIndices() {
        $('.popup-config-item').each(function(index) {
            $(this).find('.index-number').text(index + 1);
            $(this).find('.popup-select').attr('name', 'popup_config[' + index + '][popup_id]');
            $(this).find('.popup-time').attr('name', 'popup_config[' + index + '][time]');
        });
    }

    // Function to toggle popup settings visibility
    function togglePopupSettings() {
        const body = $('#popupSettingsBody');
        const icon = $('#popupIcon');

        if (body.is(':visible')) {
            body.slideUp(300);
            icon.removeClass('fi-rr-angle-small-up').addClass('fi-rr-angle-small-down');
            icon.css('transform', 'rotate(0deg)');
        } else {
            body.slideDown(300);
            icon.removeClass('fi-rr-angle-small-down').addClass('fi-rr-angle-small-up');
            icon.css('transform', 'rotate(180deg)');
        }
    }

    // Function to update popup count
    function updatePopupCount() {
        const count = $('.popup-config-item').length;
        $('.popup-count').text(count);
    }

    // Function to convert HH:MM:SS to seconds
    function timeToSeconds(timeStr) {
        if (!timeStr) return 0;

        const parts = timeStr.split(':');
        if (parts.length !== 3) return 0;

        const hours = parseInt(parts[0], 10);
        const minutes = parseInt(parts[1], 10);
        const seconds = parseInt(parts[2], 10);

        return hours * 3600 + minutes * 60 + seconds;
    }

    // Function to validate time format
    function validateTimeFormat(timeStr) {
        const regex = /^([0-9]{2}):([0-9]{2}):([0-9]{2})$/;
        if (!regex.test(timeStr)) return false;

        const parts = timeStr.split(':');
        const hours = parseInt(parts[0], 10);
        const minutes = parseInt(parts[1], 10);
        const seconds = parseInt(parts[2], 10);

        return minutes < 60 && seconds < 60;
    }

    function ajax_get_video_details(url) {
        $('#perloader').show();
        if (checkURLValidity(url)) {
            $.ajax({
                url: "{{ route('get.video.details') }}",
                type: 'POST',
                data: {
                    url: url
                },
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function (response) {
                    console.log(response);
                    jQuery('#duration').val(response.duration);
                    $('#perloader').hide();
                    $('#invalid_url').hide();
                }
            });
        } else {
            $('#invalid_url').show();
            $('#perloader').hide();
            jQuery('#duration').val('');

        }
    }

    function checkURLValidity(video_url) {
        var youtubePregMatch = /^(?:https?:\/\/)?(?:www\.|iframe\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=)((\w|-){11})|mediadelivery\.net\/embed\/[0-9]+\/[a-zA-Z0-9-]+)(?:\S+)?$/;
        var vimeoPregMatch = /^(http\:\/\/|https\:\/\/)?(www\.)?(vimeo\.com\/)([0-9]+)$/;
        if (video_url.match(youtubePregMatch)) {
            return true;
        } else if (vimeoPregMatch.test(video_url)) {
            return true;
        } else {
            return false;
        }
    }
</script>

<style>
    /* Enhanced Popup Advertisement Settings Styling */
    .popup-config-item {
        transition: all 0.3s ease;
        border-radius: 12px !important;
    }

    .popup-config-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .drag-handle {
        transition: color 0.3s ease;
    }

    .drag-handle:hover {
        color: #495057 !important;
    }

    .popup-config-item .form-select:focus,
    .popup-config-item .form-control:focus {
        border-color: #667eea !important;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
        transform: translateY(-1px);
    }

    .popup-config-item .form-select:hover {
        border-color: #667eea !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
    }

    .popup-config-item .form-select {
        cursor: pointer;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e") !important;
        background-size: 16px 12px !important;
    }

    .popup-config-item .form-select option {
        padding: 0.75rem;
        font-size: 0.95rem;
    }

    .icon-wrapper {
        transition: transform 0.3s ease;
    }

    #popupSettings:hover .icon-wrapper {
        transform: scale(1.05);
    }

    #popupIcon {
        transition: transform 0.3s ease;
    }

    .badge.bg-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border: none;
        font-weight: 500;
    }

    .btn-outline-danger:hover {
        transform: scale(1.05);
        transition: transform 0.2s ease;
    }

    #addPopupConfigBtn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .alert.alert-danger {
        border-radius: 8px;
        border: 1px solid #f5c6cb;
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
</style>

@include('admin.init')
